import { createAuthClient } from "better-auth/vue"
import { inferAdditionalFields } from "better-auth/client/plugins";
import type { auth } from "@@/auth";
// export const { signIn, signUp, useSession } = createAuthClient()
export const authClient = createAuthClient({
    /** The base URL of the server (optional if you're using the same domain) */
    baseURL: "http://localhost:3000",
    plugins: [inferAdditionalFields<typeof auth>()],
})
